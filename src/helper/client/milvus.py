import os
import re
from functools import cache
from typing import List

from langchain_core.documents import Document
from pymilvus import MilvusClient, DataType

from settings import get_or_creat_settings_ins
from src.helper.client.rerank import rerank
from src.log import logger
from src.modules.llms import LLMFactory, EmbeddingType


def extract_keywords(query_text: str) -> str:
    """
    Extract keywords from query text for full-text search.
    Remove common stop words and punctuation, keep meaningful terms.
    """
    # Remove punctuation and convert to lowercase
    cleaned_text = re.sub(r'[^\w\s]', ' ', query_text.lower())

    # Split into words and filter out common stop words
    stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '如何', '什么', '怎么', '为什么', '哪里', '什么时候',
                  '谁'}
    words = [word.strip() for word in cleaned_text.split() if
             word.strip() and word.strip() not in stop_words and len(word.strip()) > 1]

    # Join words with spaces for TEXT_MATCH
    return "数据库 预案"


@cache
def get_milvus_client() -> MilvusClient:
    config = get_or_creat_settings_ins()
    milvus_client = MilvusClient(
        uri=config.milvus.uri,
        token=f"{config.milvus.user}:{os.getenv('MILVUS__PASSWORD')}",
        db_name=config.milvus.db_name,
        timeout=config.milvus.timeout,
    )
    return milvus_client


def create_collection(collection_name):
    milvus_client = get_milvus_client()
    list_collections = milvus_client.list_collections()
    if collection_name not in list_collections:
        schema = milvus_client.create_schema(
            auto_id=True,
            enable_dynamic_field=True
        )
        schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field(field_name="source", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="title", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="token", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=2048, enable_analyzer=True,
                         enable_match=True)
        schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535)
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=768)

        # 索引参数
        index_params = milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 4096}
        )
        milvus_client.create_collection(
            collection_name=collection_name,
            schema=schema,
            index_params=index_params
        )
        logger.info(f"collection {collection_name} created")


def delete_by_source(collection_name: str, source: str):
    results = get_milvus_client().query(
        collection_name=collection_name,
        filter=f"source=='{source}'",
        output_fields=["id"]
    )
    logger.info(f"delete file: {source}")
    if len(results) > 0:
        ids = [data["id"] for data in results]
        get_milvus_client().delete(
            collection_name=collection_name,
            ids=ids
        )


def hybrid_search_optimized(query_text: str, collection_name: str) -> List[Document]:
    try:
        return get_document(query_text, collection_name)
    except Exception as e:
        logger.error(f"hybrid search error: {e}")
    return []


def _perform_vector_search(milvus_client: MilvusClient, collection_name: str, query_text: str) -> List[dict]:
    """执行向量搜索"""
    hits = milvus_client.search(
        collection_name=collection_name,
        data=[get_query_embedding(query_text)],
        limit=10,
        anns_field="vector",
        search_params={
            "metric_type": "COSINE",
            "params": {
                "nprobe": 1024,
                "radius": 0.6,
                "range_filter": 10,
            },
        },
    )[0]

    if not hits:
        return []

    return milvus_client.query(
        collection_name=collection_name,
        ids=[hit['id'] for hit in hits],
        output_fields=["id", "title", "text", "source"]
    )


def _perform_fulltext_search(milvus_client: MilvusClient, collection_name: str, query_text: str) -> List[dict]:
    """执行全文搜索"""
    # 提取关键词
    keywords = extract_keywords(query_text)
    if not keywords:
        logger.warning(f"No keywords extracted from query: {query_text}")
        return []

    logger.info(f"Performing full-text search with keywords: {keywords}")

    try:
        filter_expr = f"TEXT_MATCH(text, '{keywords}')"
        results = milvus_client.query(
            collection_name=collection_name,
            filter=filter_expr,
            limit=10,
        )
        return results
    except Exception as e:
        logger.error(f"Full-text search failed: {e}")
        return []


def _apply_rerank_and_filter(query_text: str, results: List[dict], score_threshold: float = 0.8) -> List[dict]:
    """应用重排序和分数过滤"""
    if not results:
        return []

    rerank_texts = [f"文档名称：{result['title']} \n 文档内容：{result['text']}" for result in results]
    rerank_result = rerank(query_text, rerank_texts)

    # 根据重排序分数过滤结果
    top_indices = [item['index'] for item in rerank_result if item['score'] > score_threshold]
    return [results[i] for i in top_indices]


def _convert_to_documents(results: List[dict]) -> List[Document]:
    """将查询结果转换为Document对象"""
    return [
        Document(
            page_content=result['text'],
            metadata={
                "id": result['id'],
                "title": result['title'],
                "source": result['source']
            }
        ) for result in results
    ]


def get_document(query_text: str, collection_name: str) -> List[Document]:
    """
    获取相关文档，优先使用向量搜索，如果无结果则回退到全文搜索

    Args:
        query_text: 查询文本
        collection_name: 集合名称

    Returns:
        相关文档列表
    """
    milvus_client = get_milvus_client()

    # 拆解用户意图，提取关键词


    # 1. 尝试向量搜索
    results = _perform_vector_search(milvus_client, collection_name, query_text)

    # 2. 如果向量搜索无结果，尝试全文搜索
    if not results:
        logger.info(f"Vector search returned no results, falling back to full-text search for: {query_text}")
        results = _perform_fulltext_search(milvus_client, collection_name, query_text)

        if not results:
            logger.info("Both vector and full-text search returned no results")
            return []

    # 3. 应用重排序和过滤
    filtered_results = _apply_rerank_and_filter(query_text, results)

    # 4. 转换为Document对象
    return _convert_to_documents(filtered_results)


def get_query_embedding(query_text: str) -> list[float]:
    config = get_or_creat_settings_ins()
    embedding = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)
    return embedding.embed_query(query_text)


if __name__ == '__main__':
    datas = hybrid_search_optimized(collection_name="knowledge_base_of_it_v3", query_text="云打印机如何使用")
    print(datas)
