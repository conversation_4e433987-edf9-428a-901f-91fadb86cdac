from functools import cache
from typing import List

from langchain.chat_models import init_chat_model
from langchain_community.chat_models.tongyi import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.language_models import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import BaseToolkit, BaseTool

from settings import get_or_creat_settings_ins
from src.core.rag.doa import get_all_common_knowledge_base
from src.helper.custom_chat_models import VllmQwen3ChatModel
from src.mcp.vectordb import QueryVectorDB
from src.modules.llms import LLMFactory, EmbeddingType


class VectorTookit(BaseToolkit):

    def get_tools(self) -> List[BaseTool]:
        config = get_or_creat_settings_ins()
        llm: BaseLanguageModel = get_chat_model()
        embedding = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)

        dbs = get_all_common_knowledge_base()

        # 按 query_index 去重
        unique_by_query_index = {}
        for item in dbs:
            if item.query_index not in unique_by_query_index:
                unique_by_query_index[item.query_index] = item

        # 去重后的结果列表
        deduplicated_list = list(unique_by_query_index.values())

        tools = []
        for item in deduplicated_list:
            tools.append(
                QueryVectorDB(
                    name=item.query_index,
                    doc_llm=llm,
                    em=embedding,
                    description=item.description,
                    query_index="knowledge_base_of_sre_v3_test",
                )
            )
        return tools


@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    config = get_or_creat_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]
    if llm.provider == "qwen":
        return ChatTongyi(
            top_p=llm.temperature,
            model=llm.model,
            **(llm.external_args or {}),
        )
    elif llm.provider == "vllm_qwen3":
        return VllmQwen3ChatModel(
            model=llm.model,
            temperature=llm.temperature,
            extra_body=llm.extra_body,
            **(llm.external_args or {}),
        )
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        temperature=llm.temperature,
        max_tokens=llm.max_tokens,
        **(llm.external_args or {}),
    )
