import logging
import os
from typing import Tuple, Type, Optional, Dict, Any

from dotenv import load_dotenv
from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    EnvSettingsSource,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)

from settings.toml_config import TomlConfigByEnvSettingsSource
from src.utils import get_work_dir


class LLMModel(BaseModel):
    provider: str = Field(description="The type of LLM")
    model: str = Field(description="The name of the LLM")
    temperature: float = Field(description="The temperature of the LLM", default=0.0)
    max_tokens: int = Field(description="The max tokens of the LLM", default=4096)
    external_args: Optional[Dict[str, Any]] = Field( default={}, description="The external arguments of the LLM")
    extra_body: Optional[Dict[str, Any]] = Field(default={}, description="The extra body of the LLM")


class CommonSettings(BaseSettings):
    llms: Dict[str, LLMModel] = Field(description="The LLM models used by the bot")
    model_config = SettingsConfigDict(env_file=".env", env_nested_delimiter="__")

    @classmethod
    def settings_customise_sources(
            cls,
            settings_cls: Type[BaseSettings],
            init_settings: PydanticBaseSettingsSource,
            env_settings: PydanticBaseSettingsSource,
            dotenv_settings: PydanticBaseSettingsSource,
            file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        load_dotenv()

        config_dir = os.path.join(get_work_dir(), "settings/toml")
        logging.info("config_dir:" + config_dir)

        return (
            init_settings,
            EnvSettingsSource(settings_cls),
            TomlConfigByEnvSettingsSource(settings_cls, config_dir),
        )
